import '../models/user.dart' as model_user;
import 'pocketbase_auth_service.dart';

/// 增强的认证服务类
/// 提供更好的用户体验和更强的功能
/// 现在使用 PocketBase 作为后端
class EnhancedAuthService {
  final PocketBaseAuthService _authService = PocketBaseAuthService();

  /// 获取当前登录用户
  model_user.User? get currentUser => _authService.currentUser;

  /// 检查用户是否已登录
  bool get isLoggedIn => _authService.isLoggedIn;

  /// 初始化认证服务
  Future<void> initialize() async {
    await _authService.initialize();
  }

  /// 用户登录
  Future<model_user.User?> login({
    required String email,
    required String password,
    bool rememberCredentials = true,
  }) async {
    return await _authService.login(
      email: email,
      password: password,
      rememberCredentials: rememberCredentials,
    );
  }

  /// 使用手机号登录或注册
  Future<model_user.User?> phoneLogin({
    required String phoneNumber,
    bool rememberCredentials = true,
  }) async {
    return await _authService.phoneLogin(
      phoneNumber: phoneNumber,
      rememberCredentials: rememberCredentials,
    );
  }

  /// 用户注册
  Future<model_user.User?> register({
    required String email,
    required String password,
    required String username,
    required String nickname,
    String? bio,
  }) async {
    return await _authService.register(
      email: email,
      password: password,
      username: username,
      nickname: nickname,
      bio: bio,
    );
  }

  /// 退出登录
  Future<void> logout({bool clearSavedCredentials = false}) async {
    await _authService.logout(clearSavedCredentials: clearSavedCredentials);
  }

  /// 清除保存的凭据
  Future<void> clearCredentials() async {
    await _authService.clearCredentials();
  }

  /// 检查是否需要登录
  bool requiresLogin(String action) {
    return _authService.requiresLogin(action);
  }
}
