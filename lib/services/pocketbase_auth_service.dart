import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/pocketbase_config.dart';
import '../models/user.dart' as model_user;
import 'user_service.dart';

/// PocketBase 认证服务类
/// 提供用户认证相关功能
class PocketBaseAuthService {
  final UserService _userService = UserService();

  // 缓存的登录凭据
  static const String _emailKey = 'cached_email';
  static const String _passwordKey = 'cached_password';
  static const String _phoneKey = 'cached_phone';
  static const String _autoLoginKey = 'auto_login_enabled';

  /// 获取当前登录用户
  model_user.User? get currentUser => _userService.currentUser;

  /// 检查用户是否已登录
  bool get isLoggedIn => pb.authStore.isValid;

  /// 初始化认证服务
  Future<void> initialize() async {
    // 检查是否启用了自动登录
    final prefs = await SharedPreferences.getInstance();
    final autoLoginEnabled = prefs.getBool(_autoLoginKey) ?? false;

    if (autoLoginEnabled && !isLoggedIn) {
      await _attemptAutoLogin();
    }
  }

  /// 尝试自动登录
  Future<void> _attemptAutoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString(_emailKey);
      final password = prefs.getString(_passwordKey);
      final phone = prefs.getString(_phoneKey);

      if (email != null && password != null) {
        debugPrint('尝试使用缓存的邮箱凭据自动登录');
        await login(
          email: email,
          password: password,
          rememberCredentials: false,
        );
      } else if (phone != null) {
        debugPrint('尝试使用缓存的手机号凭据自动登录');
        await phoneLogin(phoneNumber: phone, rememberCredentials: false);
      }
    } catch (e) {
      debugPrint('自动登录失败: $e');
      // 自动登录失败时清除缓存的凭据
      await clearCredentials();
    }
  }

  /// 用户登录
  Future<model_user.User?> login({
    required String email,
    required String password,
    bool rememberCredentials = true,
  }) async {
    try {
      // 使用 PocketBase 认证
      final authData = await pb
          .collection('users')
          .authWithPassword(email, password);

      // 创建用户对象
      final user = model_user.User.fromJson(authData.record.toJson());

      // 更新最后登录时间
      await pb
          .collection('users')
          .update(
            authData.record.id,
            body: {'lastLoginAt': DateTime.now().toIso8601String()},
          );

      // 更新当前用户
      await _userService.setCurrentUser(user);

      // 保存登录凭据
      if (rememberCredentials) {
        await _saveCredentials(email: email, password: password);
      }

      debugPrint('用户登录成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('登录失败: $e');
      throw Exception('登录失败: $e');
    }
  }

  /// 使用手机号登录或注册
  Future<model_user.User?> phoneLogin({
    required String phoneNumber,
    bool rememberCredentials = true,
  }) async {
    try {
      // 检查用户是否已存在
      final existingUsers = await pb
          .collection('users')
          .getList(filter: 'phone = "$phoneNumber"', perPage: 1);

      if (existingUsers.items.isNotEmpty) {
        // 用户已存在，执行登录
        final userRecord = existingUsers.items.first;

        // 创建用户对象
        final user = model_user.User.fromJson(userRecord.toJson());

        // 更新最后登录时间
        await pb
            .collection('users')
            .update(
              userRecord.id,
              body: {'lastLoginAt': DateTime.now().toIso8601String()},
            );

        // 更新当前用户
        await _userService.setCurrentUser(user);

        // 保存登录凭据
        if (rememberCredentials) {
          await _saveCredentials(phoneNumber: phoneNumber);
        }

        debugPrint('手机号登录成功: ${user.username}');
        return user;
      } else {
        // 用户不存在，执行注册
        return await _registerWithPhone(phoneNumber, rememberCredentials);
      }
    } catch (e) {
      debugPrint('手机号登录/注册失败: $e');
      throw Exception('手机号登录/注册失败: $e');
    }
  }

  /// 使用手机号注册新用户
  Future<model_user.User?> _registerWithPhone(
    String phoneNumber,
    bool rememberCredentials,
  ) async {
    try {
      // 生成随机用户名和密码
      final random = DateTime.now().millisecondsSinceEpoch % 999999;
      final randomString = random.toString().padLeft(6, '0');
      final username = 'user_$randomString';
      final email = 'phone_$<EMAIL>';
      final password = 'Phone$randomString';

      // 创建用户记录
      final userRecord = await pb
          .collection('users')
          .create(
            body: {
              'username': username,
              'email': email,
              'password': password,
              'passwordConfirm': password,
              'nickname': '用户$randomString',
              'phone': phoneNumber,
              'bio': '',
              'points': 0,
              'emailVisibility': false,
              'verified': false,
              // PocketBase 会自动管理 created 和 updated 字段
            },
          );

      // 使用创建的凭据登录
      await pb.collection('users').authWithPassword(email, password);

      // 创建用户对象
      final user = model_user.User.fromJson(userRecord.toJson());

      // 更新当前用户
      await _userService.setCurrentUser(user);

      // 保存登录凭据
      if (rememberCredentials) {
        await _saveCredentials(phoneNumber: phoneNumber);
      }

      debugPrint('手机号注册成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('手机号注册失败: $e');
      throw Exception('手机号注册失败: $e');
    }
  }

  /// 用户注册
  Future<model_user.User?> register({
    required String email,
    required String password,
    required String username,
    required String nickname,
    String? bio,
  }) async {
    try {
      debugPrint('开始创建用户记录...');
      debugPrint('用户名: $username, 邮箱: $email, 昵称: $nickname');

      // 准备创建用户的数据
      final userData = {
        'username': username,
        'email': email,
        'password': password,
        'passwordConfirm': password,
        'nickname': nickname,
        'bio': bio ?? '',
        'points': 0,
        'emailVisibility': false,
        'verified': false,
        // PocketBase 会自动管理 created 和 updated 字段
      };

      debugPrint('发送到 PocketBase 的数据: $userData');

      // 直接创建用户记录，让 PocketBase 处理重复检查
      final userRecord = await pb.collection('users').create(body: userData);

      debugPrint('用户记录创建成功，ID: ${userRecord.id}');
      debugPrint('PocketBase 返回的用户数据: ${userRecord.toJson()}');

      // 使用创建的凭据登录
      debugPrint('尝试使用新凭据登录...');
      await pb.collection('users').authWithPassword(email, password);

      // 创建用户对象
      debugPrint('准备从 JSON 创建 User 对象...');
      final user = model_user.User.fromJson(userRecord.toJson());

      // 更新当前用户
      await _userService.setCurrentUser(user);

      debugPrint('用户注册成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('注册失败详细信息: $e');

      // 分析错误类型
      String errorMessage = e.toString();
      if (errorMessage.contains('statusCode: 400')) {
        if (errorMessage.contains('username')) {
          throw Exception('用户名已被使用或格式不正确');
        } else if (errorMessage.contains('email')) {
          throw Exception('邮箱已被使用或格式不正确');
        } else {
          throw Exception('注册信息格式错误，请检查输入');
        }
      } else {
        throw Exception('注册失败: $e');
      }
    }
  }

  /// 退出登录
  Future<void> logout({bool clearSavedCredentials = false}) async {
    try {
      pb.authStore.clear();
      await _userService.clearCurrentUser();

      if (clearSavedCredentials) {
        await clearCredentials();
      }

      debugPrint('用户退出登录成功');
    } catch (e) {
      debugPrint('退出登录失败: $e');
      throw Exception('退出登录失败: $e');
    }
  }

  /// 保存登录凭据
  Future<void> _saveCredentials({
    String? email,
    String? password,
    String? phoneNumber,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    if (email != null && password != null) {
      await prefs.setString(_emailKey, email);
      await prefs.setString(_passwordKey, password);
      await prefs.remove(_phoneKey);
    } else if (phoneNumber != null) {
      await prefs.setString(_phoneKey, phoneNumber);
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
    }

    await prefs.setBool(_autoLoginKey, true);
  }

  /// 清除保存的凭据
  Future<void> clearCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_emailKey);
    await prefs.remove(_passwordKey);
    await prefs.remove(_phoneKey);
    await prefs.setBool(_autoLoginKey, false);
  }

  /// 检查是否需要登录
  bool requiresLogin(String action) {
    switch (action) {
      case 'post_spot':
      case 'add_comment':
      case 'like_spot':
      case 'profile_settings':
        return true;
      case 'view_spots':
      case 'search_spots':
      case 'view_map':
      default:
        return false;
    }
  }
}
