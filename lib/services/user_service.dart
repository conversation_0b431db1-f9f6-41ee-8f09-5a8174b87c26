import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/pocketbase_config.dart';
import '../models/user.dart';
import 'dart:async';

/// 用户服务类，用于管理用户数据
class UserService {
  static const String _currentUserKey = 'current_user';
  static const String _usersStorageKey = 'users';
  static const String _messagesStorageKey = 'messages'; // 恢复

  // 使用ValueNotifier替代直接存储用户对象
  final ValueNotifier<User?> _currentUserNotifier = ValueNotifier(null);
  ValueListenable<User?> get currentUserNotifier => _currentUserNotifier;

  List<User> _users = [];
  List<Message> _messages = []; // 恢复

  /// 获取当前登录用户
  User? get currentUser => _currentUserNotifier.value;

  /// 检查用户是否已登录
  bool isLoggedIn() =>
      _currentUserNotifier.value != null && pb.authStore.isValid;

  /// 构造函数
  UserService() {
    // 在构造函数中初始化认证监听
    _setupAuthListener();
  }

  /// 设置认证状态监听
  void _setupAuthListener() {
    // PocketBase 使用 authStore 的 onChange 监听认证状态变化
    pb.authStore.onChange.listen((event) {
      final record = pb.authStore.record;
      debugPrint('PocketBase 认证状态变化: ${record != null ? "已登录" : "已登出"}');

      if (record != null) {
        debugPrint('用户登录: ${record.id}');
        // 当用户登录时，尝试获取用户信息
        fetchCurrentUser().catchError((e) {
          debugPrint('获取用户信息失败: $e');
          return null;
        });
      } else {
        debugPrint('用户登出');
        clearCurrentUser().catchError((e) {
          debugPrint('清除用户信息失败: $e');
        });
      }
    });
  }

  /// 初始化用户服务
  Future<void> initialize() async {
    debugPrint('初始化用户服务');
    final isAuthenticated = pb.authStore.isValid;
    debugPrint('初始化时的身份验证状态: ${isAuthenticated ? "已登录" : "未登录"}');

    await _loadCurrentUser();
    debugPrint(
      '加载本地用户后状态: ${_currentUserNotifier.value != null ? "存在" : "不存在"}',
    );

    // 如果本地有用户信息但 PocketBase 没有登录，则清除本地用户信息
    if (_currentUserNotifier.value != null && !pb.authStore.isValid) {
      debugPrint('本地用户存在但 PocketBase 会话无效，清除本地用户');
      await clearCurrentUser();
    }

    // 如果 PocketBase 已登录但本地没有用户信息，则获取用户信息
    if (_currentUserNotifier.value == null && pb.authStore.isValid) {
      debugPrint('PocketBase 已登录但本地没有用户数据，尝试获取');
      try {
        await fetchCurrentUser();
        debugPrint(
          '成功从 PocketBase 获取用户: ${_currentUserNotifier.value?.username}',
        );
      } catch (e) {
        debugPrint('获取用户信息失败，尝试创建新用户: $e');

        // 如果用户在身份验证中存在但在数据库中不存在，尝试创建用户
        final authRecord = pb.authStore.record;
        if (authRecord != null) {
          final newUser = User(
            id: authRecord.id,
            username: 'user_${authRecord.id.substring(0, 8)}',
            nickname: 'user_${authRecord.id.substring(0, 8)}',
            email: authRecord.data['email'] ?? '',
            created: DateTime.now(),
            updated: DateTime.now(),
          );

          try {
            // 尝试创建用户记录
            await createUserInDatabase(newUser);
            await setCurrentUser(newUser);
            debugPrint('成功创建并设置新用户');
          } catch (createError) {
            debugPrint('创建用户失败: $createError');
            // 不创建用户以继续应用运行
          }
        }
      }
    }

    // 输出最终状态
    final isAuthenticatedFinal = pb.authStore.isValid;
    debugPrint('初始化完成后状态:');
    debugPrint('- PocketBase 登录状态: ${isAuthenticatedFinal ? "已登录" : "未登录"}');
    debugPrint(
      '- 本地用户状态: ${_currentUserNotifier.value != null ? "存在" : "不存在"}',
    );
  }

  /// 从 PocketBase 获取当前用户信息
  Future<User?> fetchCurrentUser() async {
    try {
      final authRecord = pb.authStore.record;
      if (authRecord == null) {
        debugPrint('没有登录的用户');
        return null;
      }

      debugPrint('尝试获取用户信息: ${authRecord.id}');
      final record = await pb.collection('users').getOne(authRecord.id);

      debugPrint('从 PocketBase 获取的用户数据: ${record.toJson()}');
      final user = User.fromJson(record.toJson());
      _currentUserNotifier.value = user;
      await _saveCurrentUser();
      debugPrint('成功获取用户信息: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      rethrow; // 异常往上抛出，让调用者处理
    }
  }

  /// 在数据库中创建用户
  Future<void> createUserInDatabase(User user) async {
    try {
      debugPrint('尝试创建用户记录: ${user.id}, ${user.username}');

      // 首先检查该用户是否已存在
      try {
        await pb.collection('users').getOne(user.id);
        debugPrint('用户记录已存在，不需要创建');
        return; // 用户已存在，无需创建
      } catch (e) {
        // 用户不存在，继续创建
      }

      // 检查用户名是否已存在
      String username = user.username;
      String nickname = user.nickname;

      try {
        final existingUsers = await pb
            .collection('users')
            .getList(
              page: 1,
              perPage: 1,
              filter: 'username = "${user.username}"',
            );

        if (existingUsers.items.isNotEmpty) {
          // 如果用户名已存在，添加随机字符
          nickname =
              '${user.nickname}_${DateTime.now().millisecondsSinceEpoch % 10000}';
          username =
              '${user.username}_${DateTime.now().millisecondsSinceEpoch % 10000}';
          debugPrint('用户名已存在，使用新用户名: $username');
        }
      } catch (e) {
        debugPrint('检查用户名失败: $e');
      }

      // 创建用户记录
      final record = await pb
          .collection('users')
          .create(
            body: {
              'id': user.id,
              'username': username,
              'nickname': nickname,
              'email': user.email,
              'phone': user.phoneNumber,
              'bio': user.bio,
              'avatar_url': user.avatarUrl,
              // 关系数据通过单独的集合管理，不在用户记录中存储
            },
          );

      debugPrint('用户创建成功: ${record.id}');
    } catch (e) {
      debugPrint('创建用户失败: $e');
      throw Exception('创建用户失败: $e');
    }
  }

  /// 设置当前用户
  Future<void> setCurrentUser(User user) async {
    debugPrint('设置当前用户: ${user.username}, ID: ${user.id}');
    _currentUserNotifier.value = user;
    await _saveCurrentUser();
  }

  /// 清除当前用户
  Future<void> clearCurrentUser() async {
    debugPrint('清除当前用户');

    // 清除内存中的用户对象
    final oldUserId = _currentUserNotifier.value?.id;
    _currentUserNotifier.value = null;

    try {
      // 清除SharedPreferences中的用户数据
      final prefs = await SharedPreferences.getInstance();
      final hadUser = await prefs.remove(_currentUserKey);
      debugPrint('本地用户数据移除${hadUser ? '成功' : '（数据不存在）'}');

      // 验证数据是否已经被删除
      if (prefs.getString(_currentUserKey) == null) {
        debugPrint('验证: 本地存储中的用户数据已完全清除');
      } else {
        debugPrint('警告: 本地存储中仍然存在用户数据');
      }
    } catch (e) {
      debugPrint('清除本地用户数据时出错: $e');
    }

    debugPrint('用户清除完成${oldUserId != null ? ' (ID: $oldUserId)' : ''}');
  }

  /// 根据ID获取用户
  Future<User?> getUserById(String id) async {
    try {
      final record = await pb.collection('users').getOne(id);
      return User.fromJson(record.toJson());
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 根据用户名获取用户
  Future<User?> getUserByUsername(String username) async {
    try {
      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 1, filter: 'username = "$username"');

      if (records.items.isNotEmpty) {
        return User.fromJson(records.items.first.toJson());
      }
      return null;
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  // 用户数据缓存时间戳
  DateTime _lastUsersLoadTime = DateTime.now().subtract(
    const Duration(hours: 1),
  );
  static const int _usersCacheDuration = 5 * 60 * 1000; // 5分钟

  /// 从 PocketBase 获取所有用户
  Future<List<User>> getAllUsers() async {
    // 检查缓存是否有效
    final now = DateTime.now();
    if (_users.isNotEmpty &&
        now.difference(_lastUsersLoadTime).inMilliseconds <
            _usersCacheDuration) {
      debugPrint('使用缓存的用户数据');
      return _users;
    }

    try {
      final records = await pb.collection('users').getFullList();

      // 检查响应是否为空列表
      if (records.isEmpty) {
        debugPrint('API返回空用户列表');

        // 如果有缓存，返回缓存
        if (_users.isNotEmpty) {
          return _users;
        }

        // 尝试从本地加载
        await _loadUsers();
        return _users;
      }

      // 处理正常响应
      final users =
          records.map((record) => User.fromJson(record.toJson())).toList();
      _users = users; // 更新本地缓存
      _lastUsersLoadTime = now; // 更新缓存时间戳

      // 异步保存到本地存储，不阻塞主流程
      _saveUsers().catchError((e) => debugPrint('保存用户数据失败: $e'));
      return users;
    } catch (e) {
      debugPrint('获取所有用户失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_users.isNotEmpty) {
        return _users;
      }

      // 尝试从本地加载
      await _loadUsers();
      return _users;
    }
  }

  /// 添加新用户
  Future<User> addUser(User user) async {
    _users.add(user);
    await _saveUsers();
    return user;
  }

  /// 更新用户信息
  Future<void> updateUser(User user) async {
    try {
      // 更新本地缓存
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = user;
        await _saveUsers();
      }

      // 如果是当前用户，更新当前用户缓存
      if (_currentUserNotifier.value?.id == user.id) {
        _currentUserNotifier.value = user;
        await _saveCurrentUser();
      }

      // 更新 PocketBase 数据库
      await pb
          .collection('users')
          .update(
            user.id,
            body: {
              'username': user.username,
              'nickname': user.nickname,
              'bio': user.bio,
              'phone': user.phoneNumber,
              'avatar_url': user.avatarUrl,
              // 关系数据通过单独的集合管理，不在用户记录中存储
            },
          );
    } catch (e) {
      debugPrint('更新用户信息失败: $e');
      throw Exception('更新用户信息失败: $e');
    }
  }

  /// 删除用户
  Future<void> deleteUser(String id) async {
    _users.removeWhere((user) => user.id == id);
    await _saveUsers();
  }

  /// 关注用户
  Future<void> followUser(String userId, String targetUserId) async {
    try {
      // 检查是否已经关注
      try {
        await pb
            .collection('user_follows')
            .getFirstListItem(
              'follower_id = "$userId" && following_id = "$targetUserId"',
            );
        // 如果找到记录，说明已经关注了，直接返回
        return;
      } catch (_) {
        // 没有找到记录，继续创建关注关系
      }

      // 创建关注关系
      await pb
          .collection('user_follows')
          .create(body: {'follower_id': userId, 'following_id': targetUserId});
    } catch (e) {
      debugPrint('关注用户失败: $e');
      throw Exception('关注用户失败: $e');
    }
  }

  /// 取消关注用户
  Future<void> unfollowUser(String userId, String targetUserId) async {
    try {
      // 查找并删除关注关系
      final followRecord = await pb
          .collection('user_follows')
          .getFirstListItem(
            'follower_id = "$userId" && following_id = "$targetUserId"',
          );

      await pb.collection('user_follows').delete(followRecord.id);
    } catch (e) {
      debugPrint('取消关注用户失败: $e');
      throw Exception('取消关注用户失败: $e');
    }
  }

  /// 添加发布的钓点
  Future<void> addPublishedSpot(String userId, String spotId) async {
    try {
      // 发布的钓点通过 fishing_spots 表的 user_id 字段管理
      // 这里不需要额外的操作，因为钓点创建时已经设置了 user_id
      debugPrint('钓点 $spotId 已关联到用户 $userId');
    } catch (e) {
      debugPrint('添加发布钓点失败: $e');
      throw Exception('添加发布钓点失败: $e');
    }
  }

  /// 添加收藏的钓点
  Future<void> addFavoriteSpot(String userId, String spotId) async {
    try {
      // 检查是否已经收藏
      try {
        await pb
            .collection('user_favorites')
            .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');
        // 如果找到记录，说明已经收藏了，直接返回
        return;
      } catch (_) {
        // 没有找到记录，继续创建收藏关系
      }

      // 创建收藏关系
      await pb
          .collection('user_favorites')
          .create(body: {'user_id': userId, 'spot_id': spotId});
    } catch (e) {
      debugPrint('添加收藏钓点失败: $e');
      throw Exception('添加收藏钓点失败: $e');
    }
  }

  /// 移除收藏的钓点
  Future<void> removeFavoriteSpot(String userId, String spotId) async {
    try {
      // 查找并删除收藏关系
      final favoriteRecord = await pb
          .collection('user_favorites')
          .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');

      await pb.collection('user_favorites').delete(favoriteRecord.id);
    } catch (e) {
      debugPrint('移除收藏钓点失败: $e');
      throw Exception('移除收藏钓点失败: $e');
    }
  }

  /// 获取所有消息
  Future<List<Message>> getAllMessages() async {
    if (_messages.isEmpty) {
      await _loadMessages();
    }
    return _messages;
  }

  /// 获取用户的消息
  Future<List<Message>> getUserMessages(String userId) async {
    await getAllMessages();
    return _messages
        .where(
          (message) =>
              message.senderId == userId || message.receiverId == userId,
        )
        .toList();
  }

  /// 获取两个用户之间的对话
  Future<List<Message>> getConversation(String userId1, String userId2) async {
    await getAllMessages();
    return _messages
        .where(
          (message) =>
              (message.senderId == userId1 && message.receiverId == userId2) ||
              (message.senderId == userId2 && message.receiverId == userId1),
        )
        .toList()
      ..sort((a, b) => a.sentAt.compareTo(b.sentAt));
  }

  /// 发送消息
  Future<Message> sendMessage(
    String senderId,
    String receiverId,
    String content,
  ) async {
    final message = Message(
      senderId: senderId,
      receiverId: receiverId,
      content: content,
    );

    _messages.add(message);
    await _saveMessages();
    return message;
  }

  /// 标记消息为已读
  Future<void> markMessageAsRead(String messageId) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index != -1) {
      _messages[index].isRead = true;
      await _saveMessages();
    }
  }

  /// 从本地存储加载用户数据
  Future<void> _loadUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersStorageKey);

      if (usersJson != null) {
        final List<dynamic> decodedList = jsonDecode(usersJson);
        _users = decodedList.map((item) => User.fromJson(item)).toList();
      }
    } catch (e) {
      // 处理错误，初始化为空列表
      _users = [];
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(
        _users.map((user) => user.toJson()).toList(),
      );
      await prefs.setString(_usersStorageKey, usersJson);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存用户数据失败: $e');
    }
  }

  /// 从本地存储加载当前用户
  Future<void> _loadCurrentUser() async {
    try {
      debugPrint('尝试从本地存储加载用户...');
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);

      if (userJson != null) {
        debugPrint(
          '找到本地用户数据: ${userJson.substring(0, userJson.length > 100 ? 100 : userJson.length)}...',
        );
        try {
          _currentUserNotifier.value = User.fromJson(jsonDecode(userJson));
          debugPrint(
            '成功加载本地用户: ${_currentUserNotifier.value?.username}, ID: ${_currentUserNotifier.value?.id}',
          );
        } catch (parseError) {
          debugPrint('解析用户数据失败: $parseError');
          // 如果本地数据损坏，清除并重新尝试
          await prefs.remove(_currentUserKey);
          _currentUserNotifier.value = null;
        }
      } else {
        debugPrint('本地存储中没有用户数据');
        // 尝试从 Supabase 获取
        try {
          await fetchCurrentUser();
        } catch (fetchError) {
          debugPrint('从Supabase获取用户失败: $fetchError');
        }
      }
    } catch (e) {
      debugPrint('加载当前用户失败: $e');
      _currentUserNotifier.value = null;
    }

    // 打印最终状态
    debugPrint(
      '用户加载完成. 当前状态: ${_currentUserNotifier.value != null ? "已登录" : "未登录"}',
    );
  }

  /// 保存当前用户到本地存储
  Future<void> _saveCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUserNotifier.value != null) {
        debugPrint(
          '正在保存用户到本地存储: ${_currentUserNotifier.value!.username}, ID: ${_currentUserNotifier.value!.id}',
        );
        final json = jsonEncode(_currentUserNotifier.value!.toJson());
        await prefs.setString(_currentUserKey, json);
        debugPrint('用户保存成功, JSON大小: ${json.length}字节');

        // 验证存储是否成功
        final savedJson = prefs.getString(_currentUserKey);
        if (savedJson != null) {
          debugPrint('验证: 成功从SharedPreferences读取到用户数据');
        } else {
          debugPrint('错误: 无法从SharedPreferences读取刚保存的用户数据');
        }
      } else {
        debugPrint('清除本地存储的用户信息');
        await prefs.remove(_currentUserKey);
      }
    } catch (e) {
      debugPrint('保存当前用户失败: $e');
    }
  }

  /// 从本地存储加载消息数据
  Future<void> _loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesStorageKey);

      if (messagesJson != null) {
        final List<dynamic> decodedList = jsonDecode(messagesJson);
        _messages = decodedList.map((item) => Message.fromJson(item)).toList();
      }
    } catch (e) {
      // 处理错误，初始化为空列表
      _messages = [];
    }
  }

  /// 保存消息数据到本地存储
  Future<void> _saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = jsonEncode(
        _messages.map((message) => message.toJson()).toList(),
      );
      await prefs.setString(_messagesStorageKey, messagesJson);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存消息数据失败: $e');
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      debugPrint('开始登出过程');

      // 先清除本地用户数据
      debugPrint('清除本地用户数据');
      await clearCurrentUser();

      // 然后清除 PocketBase 身份验证
      debugPrint('从 PocketBase 退出');
      pb.authStore.clear();

      // 验证是否完全退出
      final isAuthenticated = pb.authStore.isValid;
      debugPrint('退出后的身份验证状态: ${!isAuthenticated ? "已退出" : "仍然登录"}');

      if (_currentUserNotifier.value != null) {
        debugPrint('警告: 本地用户数据没有完全清除');
        // 再尝试一次清除
        _currentUserNotifier.value = null;
      }
    } catch (e) {
      debugPrint('登出失败: $e');
      throw Exception('登出失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _currentUserNotifier.dispose();
    debugPrint('UserService资源已释放');
  }

  /// 检查邮箱是否已注册
  Future<bool> isEmailRegistered(String email) async {
    try {
      // 尝试查询数据库中是否存在该邮箱
      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 1, filter: 'email = "$email"');

      return records.items.isNotEmpty;
    } catch (e) {
      debugPrint('检查邮箱是否注册失败: $e');
      return false;
    }
  }
}
