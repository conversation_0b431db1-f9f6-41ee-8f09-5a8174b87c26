import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import '../config/pocketbase_config.dart';
import '../models/fishing_spot.dart';
import '../models/spot_comment.dart';
import 'auth_service_new.dart';
import 'user_service_new.dart';

/// 钓点管理服务
/// 
/// 职责：
/// - 钓点CRUD操作
/// - 钓点搜索和筛选
/// - 地理位置查询
/// - 照片上传管理
class FishingSpotService {
  // 单例模式
  static final FishingSpotService _instance = FishingSpotService._internal();
  factory FishingSpotService() => _instance;
  FishingSpotService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();
  final UserService _userService = UserService();

  // 本地存储键
  static const String _spotsStorageKey = 'fishing_spots_cache';

  // 钓点数据缓存
  List<FishingSpot> _spotsCache = [];
  DateTime _lastAllSpotsLoadTime = DateTime.now().subtract(const Duration(hours: 1));
  
  // 区域缓存
  final Map<String, List<FishingSpot>> _regionCache = {};
  final Map<String, DateTime> _regionCacheTime = {};
  
  static const int _cacheDurationMinutes = 5;

  /// 获取当前登录用户
  User? get currentUser => _authService.currentUser;

  /// 初始化钓点服务
  Future<void> initialize() async {
    debugPrint('初始化钓点服务');
    await _loadSpotsFromLocal();
  }

  // ==================== 钓点CRUD操作 ====================

  /// 获取所有钓点
  Future<List<FishingSpot>> getAllSpots() async {
    final now = DateTime.now();
    
    // 检查缓存是否有效
    if (_spotsCache.isNotEmpty &&
        now.difference(_lastAllSpotsLoadTime).inMinutes < _cacheDurationMinutes) {
      debugPrint('使用缓存的钓点数据');
      return _spotsCache;
    }

    try {
      // 从 PocketBase 获取钓点数据
      final records = await pb
          .collection('fishing_spots')
          .getFullList(
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      // 转换为FishingSpot对象并更新缓存
      _spotsCache = _convertRecordsToSpots(records);
      _lastAllSpotsLoadTime = now;

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      // 异步保存到本地
      _saveSpotsToLocal().catchError((e) => debugPrint('保存钓点数据失败: $e'));

      return _spotsCache;
    } catch (e) {
      debugPrint('获取钓点失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_spotsCache.isNotEmpty) {
        debugPrint('使用过期的钓点缓存数据');
        return _spotsCache;
      }

      // 如果没有缓存，尝试从本地加载
      await _loadSpotsFromLocal();
      return _spotsCache;
    }
  }

  /// 根据ID获取钓点
  Future<FishingSpot?> getSpotById(String id) async {
    try {
      final record = await pb
          .collection('fishing_spots')
          .getOne(
            id,
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      final spots = _convertRecordsToSpots([record]);
      return spots.isNotEmpty ? spots.first : null;
    } catch (e) {
      debugPrint('获取钓点详情失败: $e');
      // 尝试从本地缓存获取
      try {
        return _spotsCache.firstWhere((spot) => spot.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  /// 添加新钓点
  Future<FishingSpot?> addSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      final userId = currentUser!.id;

      // 将钓点数据插入到 PocketBase
      final record = await pb
          .collection('fishing_spots')
          .create(
            body: {
              'user_id': userId,
              'name': spot.name,
              'latitude': spot.latitude,
              'longitude': spot.longitude,
              'description': spot.description,
              'address': spot.address,
              'spot_type': spot.spotType,
              'fish_types': spot.fishTypes,
              'is_public': spot.isPublic,
              'status': spot.status,
            },
          );

      // 如果有照片，上传照片
      if (spot.photoUrls.isNotEmpty) {
        for (final photoPath in spot.photoUrls) {
          await addPhotoToSpot(record.id, photoPath, false);
        }
      }

      // 如果有全景照片，上传全景照片
      if (spot.panoramaPhotoUrl != null) {
        await addPhotoToSpot(record.id, spot.panoramaPhotoUrl!, true);
      }

      // 获取完整的钓点数据
      final newSpot = await getSpotById(record.id);
      
      // 更新缓存
      if (newSpot != null) {
        _spotsCache.insert(0, newSpot);
        await _saveSpotsToLocal();
      }

      debugPrint('添加钓点成功: ${record.id}');
      return newSpot;
    } catch (e) {
      debugPrint('添加钓点失败: $e');
      return null;
    }
  }

  /// 更新钓点
  Future<bool> updateSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_spots')
          .getOne(spot.id);
      
      if (existingRecord.data['user_id'] != currentUser!.id) {
        throw Exception('无权限更新此钓点');
      }

      // 更新钓点数据
      await pb
          .collection('fishing_spots')
          .update(
            spot.id,
            body: {
              'name': spot.name,
              'latitude': spot.latitude,
              'longitude': spot.longitude,
              'description': spot.description,
              'address': spot.address,
              'spot_type': spot.spotType,
              'fish_types': spot.fishTypes,
              'is_public': spot.isPublic,
              'status': spot.status,
            },
          );

      // 更新缓存
      final index = _spotsCache.indexWhere((s) => s.id == spot.id);
      if (index != -1) {
        _spotsCache[index] = spot;
        await _saveSpotsToLocal();
      }

      debugPrint('更新钓点成功: ${spot.id}');
      return true;
    } catch (e) {
      debugPrint('更新钓点失败: $e');
      return false;
    }
  }

  /// 删除钓点
  Future<bool> deleteSpot(String spotId) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_spots')
          .getOne(spotId);
      
      if (existingRecord.data['user_id'] != currentUser!.id) {
        throw Exception('无权限删除此钓点');
      }

      // 删除钓点
      await pb.collection('fishing_spots').delete(spotId);

      // 更新缓存
      _spotsCache.removeWhere((spot) => spot.id == spotId);
      await _saveSpotsToLocal();

      debugPrint('删除钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('删除钓点失败: $e');
      return false;
    }
  }

  // ==================== 搜索和筛选 ====================

  /// 搜索钓点
  Future<List<FishingSpot>> searchSpots(String query, {int limit = 20}) async {
    try {
      final records = await pb
          .collection('fishing_spots')
          .getList(
            page: 1,
            perPage: limit,
            filter: 'name ~ "$query" || description ~ "$query" || address ~ "$query"',
            expand: 'user_id,spot_photos(spot_id)',
          );

      return _convertRecordsToSpots(records.items);
    } catch (e) {
      debugPrint('搜索钓点失败: $e');
      return [];
    }
  }

  /// 根据地理范围获取钓点
  Future<List<FishingSpot>> getSpotsInBounds({
    required double minLat,
    required double maxLat,
    required double minLng,
    required double maxLng,
    int limit = 100,
  }) async {
    final cacheKey = '${minLat}_${maxLat}_${minLng}_${maxLng}';
    final now = DateTime.now();

    // 检查区域缓存
    if (_regionCache.containsKey(cacheKey) && 
        _regionCacheTime.containsKey(cacheKey) &&
        now.difference(_regionCacheTime[cacheKey]!).inMinutes < _cacheDurationMinutes) {
      debugPrint('使用区域缓存数据');
      return _regionCache[cacheKey]!;
    }

    try {
      final records = await pb
          .collection('fishing_spots')
          .getList(
            page: 1,
            perPage: limit,
            filter: 'latitude >= $minLat && latitude <= $maxLat && longitude >= $minLng && longitude <= $maxLng',
            expand: 'user_id,spot_photos(spot_id)',
          );

      final spots = _convertRecordsToSpots(records.items);
      
      // 更新区域缓存
      _regionCache[cacheKey] = spots;
      _regionCacheTime[cacheKey] = now;

      return spots;
    } catch (e) {
      debugPrint('获取区域钓点失败: $e');
      return _regionCache[cacheKey] ?? [];
    }
  }

  /// 获取用户发布的钓点
  Future<List<FishingSpot>> getUserSpots(String userId) async {
    try {
      final records = await pb
          .collection('fishing_spots')
          .getList(
            filter: 'user_id = "$userId"',
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id)',
          );

      return _convertRecordsToSpots(records.items);
    } catch (e) {
      debugPrint('获取用户钓点失败: $e');
      return [];
    }
  }

  // ==================== 照片管理 ====================

  /// 上传照片并添加到钓点
  Future<String?> addPhotoToSpot(
    String spotId,
    String photoPath,
    bool isPanorama,
  ) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 上传文件到 PocketBase
      final file = File(photoPath);
      final formData = <String, dynamic>{
        'spot_id': spotId,
        'user_id': currentUser!.id,
        'is_panorama': isPanorama,
        'photo': file,
      };

      // 创建照片记录并上传文件
      final record = await pb.collection('spot_photos').create(body: formData);

      // 获取文件URL
      final photoUrl = pb.files.getUrl(record, record.data['photo']).toString();

      debugPrint('上传照片成功: $photoUrl');
      return photoUrl;
    } catch (e) {
      debugPrint('上传照片失败: $e');
      return null;
    }
  }

  /// 删除钓点照片
  Future<bool> deleteSpotPhoto(String photoId) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 检查照片是否属于当前用户
      final photo = await pb.collection('spot_photos').getOne(photoId);
      if (photo.data['user_id'] != currentUser!.id) {
        throw Exception('无权限删除此照片');
      }

      await pb.collection('spot_photos').delete(photoId);

      debugPrint('删除照片成功: $photoId');
      return true;
    } catch (e) {
      debugPrint('删除照片失败: $e');
      return false;
    }
  }

  // ==================== 私有方法 ====================

  /// 将PocketBase记录转换为FishingSpot对象
  List<FishingSpot> _convertRecordsToSpots(List<dynamic> records) {
    return records.map((record) {
      // 处理用户信息
      final userData = record.expand['user_id'];
      final sharedBy = userData != null ? userData.data['username'] : 'Unknown';

      // 处理照片
      final photosData = record.expand['spot_photos(spot_id)'] as List<dynamic>? ?? [];
      final photoUrls = <String>[];
      String? panoramaPhotoUrl;

      for (final photo in photosData) {
        final photoUrl = pb.files.getUrl(photo, photo.data['photo']).toString();
        if (photo.data['is_panorama'] == true) {
          panoramaPhotoUrl = photoUrl;
        } else {
          photoUrls.add(photoUrl);
        }
      }

      // 处理评论
      final commentsData = record.expand['comments(spot_id)'] as List<dynamic>? ?? [];
      final comments = commentsData.map((comment) {
        return Comment(
          id: comment.id,
          spotId: record.id,
          userId: comment.data['user_id'],
          content: comment.data['content'],
          created: DateTime.parse(comment.data['created']),
          updated: DateTime.parse(comment.data['updated']),
        );
      }).toList();

      return FishingSpot(
        id: record.id,
        name: record.data['name'],
        description: record.data['description'] ?? '',
        latitude: record.data['latitude'].toDouble(),
        longitude: record.data['longitude'].toDouble(),
        userId: record.data['user_id'],
        address: record.data['address'],
        spotType: record.data['spot_type'],
        fishTypes: record.data['fish_types'],
        isPublic: record.data['is_public'] ?? true,
        status: record.data['status'] ?? 'active',
        created: DateTime.parse(record.data['created']),
        updated: DateTime.parse(record.data['updated']),
        // 兼容性字段
        sharedBy: sharedBy,
        photoUrls: photoUrls,
        panoramaPhotoUrl: panoramaPhotoUrl,
        comments: comments,
      );
    }).toList();
  }

  /// 从本地存储加载钓点数据
  Future<void> _loadSpotsFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final spotsJson = prefs.getString(_spotsStorageKey);

      if (spotsJson != null) {
        final List<dynamic> decodedList = jsonDecode(spotsJson);
        _spotsCache = decodedList
            .map((item) => FishingSpot.fromJson(item))
            .toList();
        debugPrint('从本地加载了 ${_spotsCache.length} 个钓点');
      }
    } catch (e) {
      debugPrint('从本地加载钓点数据失败: $e');
      _spotsCache = [];
    }
  }

  /// 保存钓点数据到本地存储
  Future<void> _saveSpotsToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final spotsJson = jsonEncode(
        _spotsCache.map((spot) => spot.toJson()).toList(),
      );
      await prefs.setString(_spotsStorageKey, spotsJson);
      debugPrint('保存了 ${_spotsCache.length} 个钓点到本地');
    } catch (e) {
      debugPrint('保存钓点数据到本地失败: $e');
    }
  }

  /// 清除缓存
  void clearCache() {
    _spotsCache.clear();
    _regionCache.clear();
    _regionCacheTime.clear();
    _lastAllSpotsLoadTime = DateTime.now().subtract(const Duration(hours: 1));
  }

  /// 刷新缓存
  Future<void> refreshCache() async {
    clearCache();
    await getAllSpots();
  }
}
