import '../models/user.dart' as model_user;
import 'pocketbase_auth_service.dart';

/// 认证服务类，用于处理用户认证相关操作
/// 现在使用 PocketBase 作为后端
class AuthService {
  final PocketBaseAuthService _authService = PocketBaseAuthService();

  /// 获取当前登录用户
  model_user.User? get currentUser => _authService.currentUser;

  /// 检查用户是否已登录
  bool get isLoggedIn => _authService.isLoggedIn;

  /// 注册新用户
  Future<model_user.User?> register({
    required String email,
    required String password,
    required String username,
    required String nickname,
    String? bio,
  }) async {
    return await _authService.register(
      email: email,
      password: password,
      username: username,
      nickname: nickname,
      bio: bio,
    );
  }

  /// 用户登录
  Future<model_user.User?> login({
    required String email,
    required String password,
  }) async {
    return await _authService.login(email: email, password: password);
  }

  /// 使用手机号登录或注册
  Future<model_user.User?> phoneLogin({
    required String phoneNumber,
    String? verificationCode,
  }) async {
    return await _authService.phoneLogin(phoneNumber: phoneNumber);
  }

  /// 退出登录
  Future<void> logout() async {
    await _authService.logout();
  }
}
