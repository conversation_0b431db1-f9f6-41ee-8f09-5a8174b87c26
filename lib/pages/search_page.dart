import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import '../models/fishing_spot.dart';
import '../services/service_locator.dart';

// 带距离信息的钓点类
class _SpotWithDistance {
  final FishingSpot spot;
  final double distance;

  _SpotWithDistance({required this.spot, required this.distance});
}

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<FishingSpot> _allSpots = [];
  List<FishingSpot> _filteredSpots = [];
  List<FishingSpot> _displayedSpots = [];

  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _currentPage = 0;
  final int _pageSize = 10;

  // 用户当前位置
  late final LatLng _userLocation;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _initializeData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听器
  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreSpots();
    }
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    // 初始化位置服务
    await Services.location.initialize();

    // 获取用户当前位置
    _userLocation = Services.location.getCurrentLocation();

    // 加载钓点数据
    await _loadSpots();
  }

  // 加载钓点数据
  Future<void> _loadSpots() async {
    setState(() {
      _isLoading = true;
    });

    final spots = await Services.fishingSpot.getAllSpots();

    // 计算每个钓点与用户的距离
    final spotsWithDistance =
        spots.map((spot) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            spot.location,
          );
          return _SpotWithDistance(spot: spot, distance: distance);
        }).toList();

    // 按距离排序
    spotsWithDistance.sort((a, b) => a.distance.compareTo(b.distance));

    // 提取排序后的钓点
    final sortedSpots = spotsWithDistance.map((item) => item.spot).toList();

    setState(() {
      _allSpots = sortedSpots;
      _filteredSpots = sortedSpots;
      _currentPage = 0;
      _loadMoreSpots(initial: true);
      _isLoading = false;
    });
  }

  // 加载更多钓点
  void _loadMoreSpots({bool initial = false}) {
    if (_isLoadingMore && !initial) return;

    setState(() {
      _isLoadingMore = true;
    });

    final start = initial ? 0 : _displayedSpots.length;
    final end = start + _pageSize;

    if (start < _filteredSpots.length) {
      final newSpots = _filteredSpots.sublist(
        start,
        end > _filteredSpots.length ? _filteredSpots.length : end,
      );

      setState(() {
        if (initial) {
          _displayedSpots = newSpots;
        } else {
          _displayedSpots.addAll(newSpots);
        }
        _currentPage++;
        _isLoadingMore = false;
      });
    } else {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  // 搜索钓点
  void _searchSpots(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredSpots = _allSpots;
        _currentPage = 0;
        _loadMoreSpots(initial: true);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    final filtered =
        _allSpots.where((spot) {
          return spot.name.toLowerCase().contains(lowercaseQuery) ||
              spot.description.toLowerCase().contains(lowercaseQuery) ||
              spot.sharedBy.toLowerCase().contains(lowercaseQuery);
        }).toList();

    setState(() {
      _filteredSpots = filtered;
      _currentPage = 0;
      _loadMoreSpots(initial: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索钓点、描述或用户',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _searchSpots('');
                  },
                ),
              ),
              onChanged: _searchSpots,
            ),
          ),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredSpots.isEmpty
                    ? const Center(child: Text('没有找到匹配的钓点'))
                    : Stack(
                      children: [
                        ListView.builder(
                          controller: _scrollController,
                          itemCount:
                              _displayedSpots.length + (_isLoadingMore ? 1 : 0),
                          itemBuilder: (context, index) {
                            // 显示加载更多指示器
                            if (index == _displayedSpots.length) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            }

                            final spot = _displayedSpots[index];
                            // 计算距离
                            final distance = Services.location
                                .calculateDistanceFromCurrent(spot.location);
                            final distanceText =
                                '${distance.toStringAsFixed(1)} km';

                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                title: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        spot.name,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      distanceText,
                                      style: const TextStyle(
                                        color: Colors.blue,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                subtitle: Text(
                                  spot.description.length > 50
                                      ? '${spot.description.substring(0, 50)}...'
                                      : spot.description,
                                ),
                                trailing: const Icon(Icons.arrow_forward_ios),
                                onTap: () => _showSpotDetails(spot),
                              ),
                            );
                          },
                        ),
                        if (_displayedSpots.isEmpty && !_isLoading)
                          const Center(child: Text('没有找到匹配的钓点')),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            height: MediaQuery.of(context).size.height * 0.6,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  spot.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text('分享者: ${spot.sharedBy}'),
                const SizedBox(height: 8),
                Text('创建时间: ${spot.createdAt.toString().substring(0, 10)}'),
                const SizedBox(height: 8),
                Text(
                  '距离: ${Services.location.calculateDistanceFromCurrent(spot.location).toStringAsFixed(1)} km',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(spot.description, style: const TextStyle(fontSize: 16)),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.thumb_up),
                    const SizedBox(width: 4),
                    Text('${spot.likes}'),
                    const SizedBox(width: 16),
                    const Icon(Icons.thumb_down),
                    const SizedBox(width: 4),
                    Text('${spot.unlikes}'),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  '评论',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child:
                      spot.comments.isEmpty
                          ? const Center(child: Text('暂无评论'))
                          : ListView.builder(
                            itemCount: spot.comments.length,
                            itemBuilder: (context, index) {
                              final comment = spot.comments[index];
                              return ListTile(
                                title: Text(comment.username),
                                subtitle: Text(comment.content),
                                trailing: Text(
                                  comment.createdAt.toString().substring(0, 10),
                                ),
                              );
                            },
                          ),
                ),
              ],
            ),
          ),
    );
  }
}
