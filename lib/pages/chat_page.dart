import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/user_service.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final UserService _userService = UserService();
  List<User> _users = [];
  User? _currentUser;
  Map<String, List<Message>> _conversations = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadData();
  }

  // 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 生成示例数据
      // await _userService.generateSampleData();

      // 获取当前用户
      final currentUser = _userService.currentUser; // 修改此行

      if (currentUser == null) {
        // 静默处理未登录状态，不显示提示
        if (mounted) {
          setState(() {
            _currentUser = null;
            _users = [];
            _conversations = {};
            _isLoading = false;
          });
        }
        return;
      }

      // 获取所有用户
      final users = await _userService.getAllUsers();

      // 获取当前用户的所有消息
      final messages = await _userService.getUserMessages(currentUser.id);

      // 按对话分组消息
      final Map<String, List<Message>> conversations = {};

      for (final message in messages) {
        final otherUserId =
            message.senderId == currentUser.id
                ? message.receiverId
                : message.senderId;

        if (!conversations.containsKey(otherUserId)) {
          conversations[otherUserId] = [];
        }

        conversations[otherUserId]!.add(message);
      }

      // 对每个对话的消息按时间排序
      for (final userId in conversations.keys) {
        conversations[userId]!.sort((a, b) => b.sentAt.compareTo(a.sentAt));
      }

      if (mounted) {
        setState(() {
          _currentUser = currentUser;
          _users = users.where((user) => user.id != currentUser.id).toList();
          _conversations = conversations;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currentUser == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 24),
              Text(
                '开始聊天',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                '登录后即可与其他钓友交流',
                style: TextStyle(fontSize: 16, color: Colors.grey[500]),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/login');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                ),
                child: const Text('立即登录'),
              ),
            ],
          ),
        ),
      );
    }

    // 获取有对话的用户列表
    final conversationUserIds = _conversations.keys.toList();

    // 获取没有对话的用户列表
    final otherUsers =
        _users.where((user) => !conversationUserIds.contains(user.id)).toList();

    return Scaffold(
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              '消息',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ),

          // 有对话的用户列表
          if (conversationUserIds.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Text(
                '最近对话',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ),
            ...conversationUserIds.map((userId) {
              final user = _users.firstWhere(
                (u) => u.id == userId,
                orElse:
                    () => User(
                      id: userId,
                      username: 'unknown_user', // 提供占位符
                      nickname: '未知用户', // 提供占位符
                      email: '<EMAIL>', // 提供占位符
                      created: DateTime.now(),
                      updated: DateTime.now(),
                    ),
              );

              final lastMessage = _conversations[userId]!.first;
              final unreadCount =
                  _conversations[userId]!
                      .where(
                        (msg) =>
                            msg.senderId != _currentUser!.id && !msg.isRead,
                      )
                      .length;

              return ListTile(
                leading: CircleAvatar(
                  backgroundImage:
                      user.avatarUrl.isNotEmpty
                          ? NetworkImage(user.avatarUrl)
                          : null,
                  child: user.avatarUrl.isEmpty ? Text(user.nickname[0]) : null,
                ),
                title: Text(user.nickname),
                subtitle: Text(
                  lastMessage.content.length > 20
                      ? '${lastMessage.content.substring(0, 20)}...'
                      : lastMessage.content,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing:
                    unreadCount > 0
                        ? CircleAvatar(
                          radius: 12,
                          backgroundColor: Colors.red,
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        )
                        : Text(
                          _formatTime(lastMessage.sentAt),
                          style: const TextStyle(color: Colors.grey),
                        ),
                onTap: () => _navigateToConversation(user),
              );
            }),
            const Divider(),
          ],

          // 其他用户列表
          if (otherUsers.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Text(
                '联系人',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ),
            ...otherUsers.map(
              (user) => ListTile(
                leading: CircleAvatar(
                  backgroundImage:
                      user.avatarUrl.isNotEmpty
                          ? NetworkImage(user.avatarUrl)
                          : null,
                  child: user.avatarUrl.isEmpty ? Text(user.nickname[0]) : null,
                ),
                title: Text(user.nickname),
                subtitle: Text(user.username),
                onTap: () => _navigateToConversation(user),
              ),
            ),
          ],

          if (conversationUserIds.isEmpty && otherUsers.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32.0),
                child: Text('暂无联系人'),
              ),
            ),
        ],
      ),
    );
  }

  // 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  // 导航到对话页面
  void _navigateToConversation(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                ConversationPage(currentUser: _currentUser!, otherUser: user),
      ),
    ).then((_) => _loadData());
  }
}

// 对话页面
class ConversationPage extends StatefulWidget {
  final User currentUser;
  final User otherUser;

  const ConversationPage({
    super.key,
    required this.currentUser,
    required this.otherUser,
  });

  @override
  State<ConversationPage> createState() => _ConversationPageState();
}

class _ConversationPageState extends State<ConversationPage> {
  final UserService _userService = UserService();
  final TextEditingController _messageController = TextEditingController();
  List<Message> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  // 加载消息
  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final messages = await _userService.getConversation(
        widget.currentUser.id,
        widget.otherUser.id,
      );

      // 标记所有收到的消息为已读
      for (final message in messages) {
        if (message.senderId == widget.otherUser.id && !message.isRead) {
          await _userService.markMessageAsRead(message.id);
        }
      }

      setState(() {
        _messages = messages;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 发送消息
  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    setState(() {
      _isSending = true;
    });

    try {
      await _userService.sendMessage(
        widget.currentUser.id,
        widget.otherUser.id,
        content,
      );

      _messageController.clear();
      await _loadMessages();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发送失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.otherUser.nickname)),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _messages.isEmpty
                    ? const Center(child: Text('暂无消息'))
                    : ListView.builder(
                      reverse: true,
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[_messages.length - 1 - index];
                        final isMe = message.senderId == widget.currentUser.id;

                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 8.0,
                          ),
                          child: Row(
                            mainAxisAlignment:
                                isMe
                                    ? MainAxisAlignment.end
                                    : MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (!isMe) ...[
                                CircleAvatar(
                                  backgroundImage:
                                      widget.otherUser.avatarUrl.isNotEmpty
                                          ? NetworkImage(
                                            widget.otherUser.avatarUrl,
                                          )
                                          : null,
                                  child:
                                      widget.otherUser.avatarUrl.isEmpty
                                          ? Text(widget.otherUser.nickname[0])
                                          : null,
                                ),
                                const SizedBox(width: 8),
                              ],

                              Flexible(
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color:
                                        isMe
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[300],
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        message.content,
                                        style: TextStyle(
                                          color:
                                              isMe
                                                  ? Colors.white
                                                  : Colors.black,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        _formatDateTime(message.sentAt),
                                        style: TextStyle(
                                          color:
                                              isMe
                                                  ? Colors.white.withValues(
                                                    alpha: 0.7,
                                                  )
                                                  : Colors.black.withValues(
                                                    alpha: 0.7,
                                                  ),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              if (isMe) ...[
                                const SizedBox(width: 8),
                                CircleAvatar(
                                  backgroundImage:
                                      widget.currentUser.avatarUrl.isNotEmpty
                                          ? NetworkImage(
                                            widget.currentUser.avatarUrl,
                                          )
                                          : null,
                                  child:
                                      widget.currentUser.avatarUrl.isEmpty
                                          ? Text(widget.currentUser.nickname[0])
                                          : null,
                                ),
                              ],
                            ],
                          ),
                        );
                      },
                    ),
          ),

          // 输入框
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _isSending ? null : _sendMessage,
                  icon:
                      _isSending
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.send),
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      return '今天 ${_formatTimeOnly(dateTime)}';
    } else if (messageDate == yesterday) {
      return '昨天 ${_formatTimeOnly(dateTime)}';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${_formatTimeOnly(dateTime)}';
    }
  }

  // 格式化时间
  String _formatTimeOnly(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
