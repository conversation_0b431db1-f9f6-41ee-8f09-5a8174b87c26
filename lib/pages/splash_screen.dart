import 'dart:async';
import 'package:flutter/material.dart';
import '../services/service_locator.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _animation;

  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 初始化状态
  bool _isInitialized = false;
  String _statusMessage = '正在初始化...';

  @override
  void initState() {
    super.initState();

    // 初始化动画
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();

    // 初始化数据
    _initializeData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // 异步函数，初始化数据
  Future<void> _initializeData() async {
    // 添加超时守护，确保即使有问题也能执行完成
    Timer timer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isInitialized = true;
          _statusMessage = '初始化超时，继续启动应用';
        });
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/main');
          }
        });
      }
    });

    try {
      // 并行初始化位置服务和用户服务
      setState(() {
        _statusMessage = '正在初始化应用...';
      });

      // 使用try-catch分别执行各个初始化，允许部分失败
      // 初始化位置服务
      try {
        await Services.location.initialize();
        if (mounted) {
          setState(() {
            _statusMessage = '位置服务初始化完成';
          });
        }
      } catch (e) {
        debugPrint('位置服务初始化失败: $e');
        // 位置服务失败不影响应用继续
      }

      // 初始化用户服务
      try {
        await Services.user.initialize();
        if (mounted) {
          setState(() {
            _statusMessage = '用户服务初始化完成';
          });
        }
      } catch (e) {
        debugPrint('用户服务初始化失败: $e');
        // 用户服务失败不影响应用继续
      }

      // 初始化认证服务
      try {
        await Services.auth.initialize();
        if (mounted) {
          setState(() {
            _statusMessage = '认证服务初始化完成';
          });
        }
      } catch (e) {
        debugPrint('认证服务初始化失败: $e');
        // 认证服务失败不影响应用继续
      }

      // 标记初始化完成
      if (mounted) {
        setState(() {
          _isInitialized = true;
          _statusMessage = '初始化完成';
        });
      }

      // 取消超时守护
      timer.cancel();

      // 标记完成后等待至少 1 秒钟以显示完成状态
      await Future.delayed(const Duration(milliseconds: 800));

      // 无论是否登录，都直接跳转到主页
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/main');
      }
    } catch (e) {
      debugPrint('初始化出错: $e');
      if (mounted) {
        setState(() {
          _statusMessage = '初始化失败，仍然继续...';
          _isInitialized = true;
        });
      }

      // 取消超时守护
      timer.cancel();

      // 出错时，等待 1 秒后继续跳转到主页
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/main');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo 动画
            FadeTransition(
              opacity: _animation,
              child: ScaleTransition(
                scale: _animation,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Center(
                    child: Text(
                      '鱼窝子',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 40),
            // 加载状态
            Text(
              _statusMessage,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 20),
            // 加载进度条
            SizedBox(
              width: 200,
              child: LinearProgressIndicator(
                value: _isInitialized ? 1.0 : null,
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
