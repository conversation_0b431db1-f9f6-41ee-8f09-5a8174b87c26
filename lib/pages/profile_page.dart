import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/service_locator.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool _isLoggingOut = false;

  @override
  void initState() {
    super.initState();
    // 不再需要_loadUserData，使用ValueListenableBuilder自动响应状态变化
  }

  // 退出登录
  Future<void> _logout() async {
    if (_isLoggingOut) return; // 防止重复点击

    setState(() {
      _isLoggingOut = true;
    });

    try {
      debugPrint('开始退出登录操作');
      await Services.auth.logout();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('退出登录成功')));
      }
    } catch (e) {
      debugPrint('退出登录异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('退出登录失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoggingOut = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<User?>(
      valueListenable: Services.auth.currentUserNotifier,
      builder: (context, currentUser, child) {
        // 如果正在登出，显示加载指示器
        if (_isLoggingOut) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在退出登录...'),
                ],
              ),
            ),
          );
        }

        // 如果没有登录，显示未登录视图
        if (currentUser == null) {
          return _buildNotLoggedInView();
        }

        // 如果已经登录，显示已登录视图
        return _buildLoggedInView(currentUser);
      },
    );
  }

  // 构建未登录视图
  Widget _buildNotLoggedInView() {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.blue),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('个人中心'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 顶部蓝色区域
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 30),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // 默认头像
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade300,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 数据统计
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatItem('0', '漂豆'),
                _buildStatItem('0', '发布'),
                _buildStatItem('0', '收藏'),
              ],
            ),

            const SizedBox(height: 20),

            // 功能按钮
            _buildFunctionButton('关注', Colors.blue.shade100, Icons.people),
            _buildFunctionButton('兑换', Colors.green.shade100, Icons.swap_horiz),
            _buildFunctionButton('设置', Colors.yellow.shade100, Icons.settings),

            const SizedBox(height: 20),

            // 登录按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: ElevatedButton(
                onPressed: () => Navigator.pushNamed(context, '/login'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text('登录', style: TextStyle(fontSize: 16)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建已登录视图
  Widget _buildLoggedInView(User user) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.blue),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('个人中心'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 顶部蓝色区域
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 30),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // 用户头像
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.blue.shade300,
                    backgroundImage:
                        user.avatarUrl.isNotEmpty
                            ? NetworkImage(user.avatarUrl)
                            : null,
                    child:
                        user.avatarUrl.isEmpty
                            ? Text(
                              user.nickname[0],
                              style: const TextStyle(
                                fontSize: 40,
                                color: Colors.white,
                              ),
                            )
                            : null,
                  ),

                  const SizedBox(height: 10),

                  // 用户名
                  Text(
                    user.nickname,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 数据统计
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatItem('0', '漂豆'),
                _buildStatItem(
                  '0', // TODO: 实现通过 fishing_spots 集合查询发布数量
                  '发布',
                ),
                _buildStatItem(
                  '0', // TODO: 实现通过 user_favorites 集合查询收藏数量
                  '收藏',
                ),
              ],
            ),

            const SizedBox(height: 20),

            // 功能按钮
            _buildFunctionButton('兑换', Colors.green.shade100, Icons.swap_horiz),
            _buildFunctionButton('关注', Colors.blue.shade100, Icons.people),
            _buildFunctionButton('设置', Colors.yellow.shade100, Icons.settings),

            const SizedBox(height: 20),

            // 退出登录按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: ElevatedButton(
                onPressed: _logout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text('退出登录', style: TextStyle(fontSize: 16)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建统计项
  Widget _buildStatItem(String count, String label) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            count,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 5),
          Text(
            label,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  // 构建功能按钮
  Widget _buildFunctionButton(String label, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    );
  }
}
