import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import '../../models/user.dart';

/// 服务测试页面
/// 
/// 用于测试新的服务架构功能
class ServiceTestPage extends StatefulWidget {
  const ServiceTestPage({super.key});

  @override
  State<ServiceTestPage> createState() => _ServiceTestPageState();
}

class _ServiceTestPageState extends State<ServiceTestPage> {
  String _statusMessage = '准备就绪';
  User? _currentUser;
  List<User> _users = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _statusMessage = '初始化服务中...';
      _isLoading = true;
    });

    try {
      // 监听当前用户变化
      Services.auth.currentUserNotifier.addListener(_onUserChanged);
      _currentUser = Services.auth.currentUser;
      
      setState(() {
        _statusMessage = '服务初始化完成';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '服务初始化失败: $e';
        _isLoading = false;
      });
    }
  }

  void _onUserChanged() {
    setState(() {
      _currentUser = Services.auth.currentUser;
    });
  }

  @override
  void dispose() {
    Services.auth.currentUserNotifier.removeListener(_onUserChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('服务架构测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息
            _buildStatusCard(),
            const SizedBox(height: 16),
            
            // 服务状态
            _buildServiceStatusCard(),
            const SizedBox(height: 16),
            
            // 认证服务测试
            _buildAuthTestCard(),
            const SizedBox(height: 16),
            
            // 用户服务测试
            _buildUserTestCard(),
            const SizedBox(height: 16),
            
            // 社交服务测试
            _buildSocialTestCard(),
            const SizedBox(height: 16),
            
            // 钓点服务测试
            _buildFishingSpotTestCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '系统状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('状态: $_statusMessage'),
            const SizedBox(height: 8),
            Text('当前用户: ${_currentUser?.nickname ?? '未登录'}'),
            if (_isLoading) const LinearProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '服务状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildServiceStatusItem('认证服务', serviceLocator.isInitialized<AuthService>()),
            _buildServiceStatusItem('用户服务', serviceLocator.isInitialized<UserService>()),
            _buildServiceStatusItem('社交服务', serviceLocator.isInitialized<SocialService>()),
            _buildServiceStatusItem('钓点服务', serviceLocator.isInitialized<FishingSpotService>()),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                serviceLocator.printServiceStatus();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('服务状态已打印到控制台')),
                );
              },
              child: const Text('打印服务状态'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatusItem(String name, bool isInitialized) {
    return Row(
      children: [
        Icon(
          isInitialized ? Icons.check_circle : Icons.error,
          color: isInitialized ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(name),
      ],
    );
  }

  Widget _buildAuthTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '认证服务测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('登录状态: ${Services.auth.isLoggedIn ? '已登录' : '未登录'}'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _testLogin,
                  child: const Text('测试登录'),
                ),
                ElevatedButton(
                  onPressed: _testLogout,
                  child: const Text('测试登出'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '用户服务测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('缓存用户数: ${_users.length}'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _testGetAllUsers,
                  child: const Text('获取所有用户'),
                ),
                ElevatedButton(
                  onPressed: _testSearchUsers,
                  child: const Text('搜索用户'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '社交服务测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('社交功能需要登录后测试'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: Services.auth.isLoggedIn ? _testSocialFeatures : null,
                  child: const Text('测试社交功能'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFishingSpotTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '钓点服务测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('钓点数据管理功能'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _testGetAllSpots,
                  child: const Text('获取所有钓点'),
                ),
                ElevatedButton(
                  onPressed: _testSearchSpots,
                  child: const Text('搜索钓点'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // ==================== 测试方法 ====================

  Future<void> _testLogin() async {
    try {
      setState(() => _isLoading = true);
      
      // 这里使用测试账号，实际应用中应该有登录表单
      final user = await Services.auth.login(
        email: '<EMAIL>',
        password: 'testpassword',
      );
      
      setState(() {
        _statusMessage = user != null ? '登录成功' : '登录失败';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '登录失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testLogout() async {
    try {
      await Services.auth.logout();
      setState(() => _statusMessage = '登出成功');
    } catch (e) {
      setState(() => _statusMessage = '登出失败: $e');
    }
  }

  Future<void> _testGetAllUsers() async {
    try {
      setState(() => _isLoading = true);
      
      final users = await Services.user.getAllUsers();
      
      setState(() {
        _users = users;
        _statusMessage = '获取到 ${users.length} 个用户';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '获取用户失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSearchUsers() async {
    try {
      setState(() => _isLoading = true);
      
      final users = await Services.user.searchUsers('test');
      
      setState(() {
        _statusMessage = '搜索到 ${users.length} 个用户';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '搜索用户失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSocialFeatures() async {
    setState(() => _statusMessage = '社交功能测试完成（模拟）');
  }

  Future<void> _testGetAllSpots() async {
    try {
      setState(() => _isLoading = true);
      
      final spots = await Services.fishingSpot.getAllSpots();
      
      setState(() {
        _statusMessage = '获取到 ${spots.length} 个钓点';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '获取钓点失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSearchSpots() async {
    try {
      setState(() => _isLoading = true);
      
      final spots = await Services.fishingSpot.searchSpots('钓点');
      
      setState(() {
        _statusMessage = '搜索到 ${spots.length} 个钓点';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '搜索钓点失败: $e';
        _isLoading = false;
      });
    }
  }
}
