import 'package:flutter/material.dart';
import '../../services/fishing_spot_service.dart';
import '../../services/pocketbase_auth_service.dart';
import '../../models/user.dart' as model_user;
import '../../models/fishing_spot.dart';
import '../../config/pocketbase_config.dart';

/// PocketBase功能测试页面
/// 仅在开发模式下可用，用于测试各种PocketBase后端功能
class PocketBaseTestPage extends StatefulWidget {
  const PocketBaseTestPage({super.key});

  @override
  State<PocketBaseTestPage> createState() => _PocketBaseTestPageState();
}

class _PocketBaseTestPageState extends State<PocketBaseTestPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 服务实例
  final FishingSpotService _spotService = FishingSpotService();
  final PocketBaseAuthService _authService = PocketBaseAuthService();

  // 测试数据控制器
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();

  // 状态变量
  bool _isLoading = false;
  String _statusMessage = '';
  List<model_user.User> _users = [];
  List<FishingSpot> _spots = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeTestData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _usernameController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  void _initializeTestData() {
    _emailController.text = '<EMAIL>';
    _passwordController.text = 'test123456';
    _usernameController.text = 'testuser';
    _nicknameController.text = '测试用户';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PocketBase 测试'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '系统信息'),
            Tab(text: '注册测试'),
            Tab(text: '用户管理'),
            Tab(text: '钓点管理'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSystemInfoTab(),
          _buildRegisterTestTab(),
          _buildUserManagementTab(),
          _buildSpotManagementTab(),
        ],
      ),
    );
  }

  ///////////////////////////////////////////////////////////// 构建系统信息标签页
  Widget _buildSystemInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'PocketBase 连接信息',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Text('服务器地址: ${PocketBaseConfig.instance.serverUrl}'),
                  Text(
                    '初始化状态: ${PocketBaseConfig.instance.isInitialized ? '已初始化' : '未初始化'}',
                  ),
                  Text('认证状态: ${_authService.isLoggedIn ? '已登录' : '未登录'}'),
                  if (_authService.isLoggedIn)
                    Text('当前用户: ${_authService.currentUser?.username ?? '无'}'),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testConnection,
                          child: const Text('测试服务器连接'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _checkUsersCollection,
                          child: const Text('检查用户集合'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///////////////////////////////////////////////////////////// 构建注册测试标签页
  Widget _buildRegisterTestTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 注册状态信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '用户注册测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text('在此页面可以测试新用户注册功能'),
                  Text('当前登录状态: ${_authService.isLoggedIn ? '已登录' : '未登录'}'),
                  if (_authService.isLoggedIn)
                    Text('当前用户: ${_authService.currentUser?.username ?? '无'}'),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      border: Border.all(color: Colors.orange.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.warning,
                              color: Colors.orange.shade700,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '注意事项',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          '如果注册失败，请先在"系统信息"标签页测试服务器连接',
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 注册测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('新用户注册', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      border: OutlineInputBorder(),
                      hintText: '请输入用户名',
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: '邮箱',
                      border: OutlineInputBorder(),
                      hintText: '请输入邮箱地址',
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      border: OutlineInputBorder(),
                      hintText: '请输入密码',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _nicknameController,
                    decoration: const InputDecoration(
                      labelText: '昵称',
                      border: OutlineInputBorder(),
                      hintText: '请输入昵称（可选）',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testRegister,
                          child: const Text('测试注册'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _clearRegisterForm,
                          child: const Text('清空表单'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 状态信息
          if (_statusMessage.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试结果',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  ///////////////////////////////////////////////////////////// 构建用户管理标签页
  Widget _buildUserManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 用户列表
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '用户列表 (${_users.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _refreshUsers,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_users.isEmpty)
                    const Text('暂无用户数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _users.length,
                      itemBuilder: (context, index) {
                        final user = _users[index];
                        return ListTile(
                          title: Text(user.username),
                          subtitle: Text(user.email),
                          trailing: Text(
                            user.nickname.isEmpty ? '无昵称' : user.nickname,
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 创建测试用户
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '创建测试用户',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _nicknameController,
                    decoration: const InputDecoration(
                      labelText: '昵称',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _createTestUser,
                    child: const Text('创建测试用户'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///////////////////////////////////////////////////////////// 构建钓点管理标签页
  Widget _buildSpotManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 钓点列表
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '钓点列表 (${_spots.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _refreshSpots,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_spots.isEmpty)
                    const Text('暂无钓点数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _spots.length,
                      itemBuilder: (context, index) {
                        final spot = _spots[index];
                        return ListTile(
                          title: Text(spot.name),
                          subtitle: Text(
                            '${spot.latitude.toStringAsFixed(4)}, ${spot.longitude.toStringAsFixed(4)}',
                          ),
                          trailing: Text(spot.userId), // 显示用户ID，实际应该通过关联查询获取用户名
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }


  ///////////////////////////////////////////////////////////////////// 测试注册
  Future<void> _testRegister() async {
    // 验证输入
    if (_usernameController.text.trim().isEmpty) {
      _updateStatus('请输入用户名');
      return;
    }
    if (_emailController.text.trim().isEmpty) {
      _updateStatus('请输入邮箱');
      return;
    }
    if (_passwordController.text.trim().isEmpty) {
      _updateStatus('请输入密码');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试注册...';
    });

    try {
      debugPrint('开始注册用户...');
      debugPrint('用户名: ${_usernameController.text.trim()}');
      debugPrint('邮箱: ${_emailController.text.trim()}');
      debugPrint(
        '昵称: ${_nicknameController.text.trim().isEmpty ? _usernameController.text.trim() : _nicknameController.text.trim()}',
      );

      final user = await _authService.register(
        username: _usernameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        nickname:
            _nicknameController.text.trim().isEmpty
                ? _usernameController.text.trim()
                : _nicknameController.text.trim(),
      );

      if (user != null) {
        _updateStatus('注册成功: ${user.username} (${user.email})');
        // 注册成功后清空表单
        _clearRegisterForm();
      } else {
        _updateStatus('注册失败: 未知错误');
      }
    } catch (e) {
      debugPrint('注册失败详细错误: $e');

      String errorMessage = _analyzeRegisterError(e.toString());
      _updateStatus('注册失败: $errorMessage');

      // 显示详细错误分析对话框
      if (mounted) {
        _showRegisterErrorDialog(e.toString());
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////// 清空注册表单
  void _clearRegisterForm() {
    _usernameController.clear();
    _emailController.clear();
    _passwordController.clear();
    _nicknameController.clear();
    setState(() {
      _statusMessage = '表单已清空';
    });
  }

  ////////////////////////////////////////////////////////////////// 刷新用户列表
  Future<void> _refreshUsers() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载用户列表...';
    });

    try {
      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 50, sort: '-created');

      _users =
          records.items.map((record) {
            return model_user.User.fromJson(record.toJson());
          }).toList();

      _updateStatus('用户列表加载成功，共 ${_users.length} 个用户');
    } catch (e) {
      _updateStatus('加载用户列表失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////// 创建测试用户
  Future<void> _createTestUser() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在创建测试用户...';
    });

    try {
      final user = await _authService.register(
        email: '${_usernameController.text}@test.com',
        password: 'test123456',
        username: _usernameController.text,
        nickname: _nicknameController.text,
      );

      if (user != null) {
        _updateStatus('测试用户创建成功: ${user.username}');
        await _refreshUsers();
      } else {
        _updateStatus('创建测试用户失败');
      }
    } catch (e) {
      _updateStatus('创建测试用户失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////// 刷新钓点列表
  Future<void> _refreshSpots() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载钓点列表...';
    });

    try {
      _spots = await _spotService.getAllSpots();
      _updateStatus('钓点列表加载成功，共 ${_spots.length} 个钓点');
    } catch (e) {
      _updateStatus('加载钓点列表失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ///////////////////////////////////////////////////////////////// 测试服务器连接
  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试服务器连接...';
    });

    try {
      // 测试健康检查端点
      await pb.health.check();
      _updateStatus('服务器连接成功！');
    } catch (e) {
      debugPrint('服务器连接失败: $e');
      _updateStatus('服务器连接失败: $e');

      // 提供详细的错误分析
      String errorAnalysis = _analyzeConnectionError(e.toString());

      // 显示详细错误信息对话框
      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('连接失败分析'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '错误详情:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(e.toString()),
                      const SizedBox(height: 16),
                      const Text(
                        '可能原因:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(errorAnalysis),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('确定'),
                  ),
                ],
              ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////// 分析连接错误
  String _analyzeConnectionError(String error) {
    if (error.contains('Connection refused')) {
      return '1. PocketBase 服务器未启动\n2. 服务器地址或端口错误\n3. 防火墙阻止连接\n4. 网络连接问题';
    } else if (error.contains('SocketException')) {
      return '1. 网络连接不可用\n2. DNS 解析失败\n3. 服务器地址错误';
    } else if (error.contains('TimeoutException')) {
      return '1. 网络连接超时\n2. 服务器响应缓慢\n3. 网络不稳定';
    } else {
      return '1. 检查服务器地址配置\n2. 确认 PocketBase 服务正常运行\n3. 检查网络连接状态';
    }
  }

  ////////////////////////////////////////////////////////////////// 分析注册错误
  String _analyzeRegisterError(String error) {
    if (error.contains('statusCode: 400')) {
      if (error.contains('username')) {
        return '用户名格式错误或已被使用';
      } else if (error.contains('email')) {
        return '邮箱格式错误或已被使用';
      } else if (error.contains('password')) {
        return '密码不符合要求（可能太短或太简单）';
      } else {
        return '请求数据格式错误，请检查输入信息';
      }
    } else if (error.contains('statusCode: 403')) {
      return '没有权限创建用户，请检查 PocketBase 权限设置';
    } else if (error.contains('statusCode: 422')) {
      return '数据验证失败，请检查必填字段';
    } else if (error.contains('Something went wrong')) {
      return 'PocketBase 服务器内部错误，可能是集合配置问题';
    } else {
      return '未知错误，请检查服务器日志';
    }
  }

  ////////////////////////////////////////////////////////// 显示注册错误详情对话框
  void _showRegisterErrorDialog(String error) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('注册失败详情'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    '错误信息:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(error, style: const TextStyle(fontSize: 12)),
                  const SizedBox(height: 16),
                  const Text(
                    '建议解决方案:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(_analyzeRegisterError(error)),
                  const SizedBox(height: 16),
                  const Text(
                    '调试步骤:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. 检查 PocketBase Admin UI 中的 users 集合配置\n'
                    '2. 确认所有必需字段都已正确设置\n'
                    '3. 检查字段验证规则\n'
                    '4. 确认创建权限已正确配置\n'
                    '5. 查看 PocketBase 服务器日志获取详细错误',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /////////////////////////////////////////////////////////////// 检查用户集合配置
  Future<void> _checkUsersCollection() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在检查用户集合配置...';
    });

    try {
      // 尝试获取用户集合的第一条记录（如果存在）
      final result = await pb.collection('users').getList(page: 1, perPage: 1);

      _updateStatus('用户集合检查成功！当前有 ${result.totalItems} 个用户');

      // 显示集合信息对话框
      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('用户集合信息'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('总用户数: ${result.totalItems}'),
                      Text('每页显示: ${result.perPage}'),
                      Text('总页数: ${result.totalPages}'),
                      const SizedBox(height: 16),
                      const Text(
                        '集合状态: 正常',
                        style: TextStyle(color: Colors.green),
                      ),
                      const SizedBox(height: 8),
                      const Text('✓ 可以正常访问 users 集合'),
                      const Text('✓ 集合权限配置正确'),
                      if (result.items.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        const Text(
                          '示例用户字段:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          result.items.first.toJson().keys.join(', '),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('确定'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      debugPrint('检查用户集合失败: $e');
      _updateStatus('用户集合检查失败: $e');

      // 显示详细错误信息
      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('集合检查失败'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '错误详情:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(e.toString(), style: const TextStyle(fontSize: 12)),
                      const SizedBox(height: 16),
                      const Text(
                        '可能原因:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '1. users 集合不存在\n'
                        '2. 集合权限配置错误\n'
                        '3. PocketBase 服务器配置问题\n'
                        '4. 数据库连接问题',
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '解决方案:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '1. 在 PocketBase Admin UI 中创建 users 集合\n'
                        '2. 配置正确的字段和权限\n'
                        '3. 确保集合可以被访问',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('确定'),
                  ),
                ],
              ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////// 更新状态信息
  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });

    // 显示snackbar
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}
