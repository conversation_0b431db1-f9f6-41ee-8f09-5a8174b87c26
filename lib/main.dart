import 'package:flutter/material.dart';
import 'pages/splash_screen.dart';
import 'pages/login_page.dart';
import 'pages/main_screen.dart';
import 'pages/dev/pocketbase_test_page.dart';
import 'pages/dev/service_test_page.dart';
import 'config/app_config.dart';
import 'config/pocketbase_config.dart';
import 'services/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 PocketBase 客户端（允许失败）
  try {
    await PocketBaseConfig.instance.initialize();
  } catch (e) {
    // PocketBase 初始化失败时，记录错误但不阻止应用启动
    debugPrint('PocketBase 初始化失败: $e');
    debugPrint('应用将在离线模式下启动');
  }

  // 初始化服务定位器
  try {
    debugPrint('开始初始化服务架构...');
    await serviceLocator.registerServices();
    await serviceLocator.initializeServices();
    serviceLocator.printServiceStatus();
    debugPrint('服务架构初始化完成');
  } catch (e) {
    debugPrint('服务架构初始化失败: $e');
    debugPrint('应用将使用降级模式启动');
  }

  // 打印配置信息（仅在开发模式下）
  AppConfig.instance.printConfigInfo();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '鱼窝子',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 0, 136, 255),
        ),
        useMaterial3: true,
      ),
      initialRoute: '/splash',
      routes: {
        '/splash': (context) => const SplashScreen(),
        '/login': (context) => const LoginPage(),
        '/main': (context) => const MainScreen(),
        // 开发页面路由（仅在开发模式下可用）
        if (AppConfig.instance.enablePocketBaseTestPage)
          '/dev/pocketbase-test': (context) => const PocketBaseTestPage(),
        if (AppConfig.instance.enableDeveloperTools)
          '/dev/service-test': (context) => const ServiceTestPage(),
      },
    );
  }
}
