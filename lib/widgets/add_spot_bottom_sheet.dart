import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';
import '../models/fishing_spot.dart';
import '../services/fishing_spot_service.dart';
import '../services/pocketbase_auth_service.dart';

/// 添加钓点底部弹出表单
class AddSpotBottomSheet extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加钓点回调
  final Function(FishingSpot) onSpotAdded;

  const AddSpotBottomSheet({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
  });

  @override
  State<AddSpotBottomSheet> createState() => _AddSpotBottomSheetState();
}

class _AddSpotBottomSheetState extends State<AddSpotBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  final FishingSpotService _fishingSpotService = FishingSpotService();
  final PocketBaseAuthService _authService = PocketBaseAuthService();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isLoading = false;
  final List<String> _photoFilePaths = [];
  String? _panoramaFilePath;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和关闭按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '添加钓点',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: widget.onClose,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 位置信息
                    Text(
                      '位置: ${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                    const Text(
                      '提示: 您可以移动地图来微调钓点位置',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                    const SizedBox(height: 16),

                    // 钓点名称
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: '钓点名称',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入钓点名称';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 钓点描述
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: '钓点描述',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入钓点描述';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 照片上传
                    const Text(
                      '上传照片',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // 照片预览
                    SizedBox(
                      height: 100,
                      child: Row(
                        children: [
                          // 添加照片按钮
                          GestureDetector(
                            onTap: _pickImage,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.add_a_photo,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),

                          // 照片列表
                          Expanded(
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: _photoFilePaths.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Stack(
                                    children: [
                                      Image.file(
                                        File(_photoFilePaths[index]),
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                      ),
                                      Positioned(
                                        right: 0,
                                        top: 0,
                                        child: GestureDetector(
                                          onTap: () => _removePhoto(index),
                                          child: Container(
                                            padding: const EdgeInsets.all(2),
                                            decoration: const BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.close,
                                              size: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 全景照片
                    Row(
                      children: [
                        const Text(
                          '全景照片',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: _pickPanoramaImage,
                          child: const Text('选择全景照片'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // 全景照片预览
                    if (_panoramaFilePath != null)
                      Stack(
                        children: [
                          Image.file(
                            File(_panoramaFilePath!),
                            height: 100,
                            width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                          Positioned(
                            right: 0,
                            top: 0,
                            child: GestureDetector(
                              onTap: _removePanoramaPhoto,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 20,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                    const Spacer(),

                    // 提交按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('发布钓点'),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  // 选择照片
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _photoFilePaths.add(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('选择照片失败: $e')));
      }
    }
  }

  // 选择全景照片
  Future<void> _pickPanoramaImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _panoramaFilePath = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('选择全景照片失败: $e')));
      }
    }
  }

  // 移除照片
  void _removePhoto(int index) {
    setState(() {
      _photoFilePaths.removeAt(index);
    });
  }

  // 移除全景照片
  void _removePanoramaPhoto() {
    setState(() {
      _panoramaFilePath = null;
    });
  }

  // 提交表单
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    // 检查用户是否已登录
    if (!_authService.isLoggedIn) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先登录后再发布钓点')));
      return;
    }

    final user = _authService.currentUser;

    setState(() {
      _isLoading = true;
    });

    try {
      // 创建钓点对象
      final spot = FishingSpot(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        latitude: widget.location.latitude,
        longitude: widget.location.longitude,
        userId: user?.id ?? '',
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      // 添加钓点
      final addedSpot = await _fishingSpotService.addSpot(spot);

      if (addedSpot != null) {
        // 添加成功，调用回调
        widget.onSpotAdded(addedSpot);

        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('钓点发布成功！')));
          widget.onClose();
        }
      } else {
        throw Exception('添加钓点失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发布失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
