# 鱼窝子应用服务架构文档

## 概述

本文档描述了鱼窝子应用重构后的服务架构，基于单一职责原则和最佳实践设计，提供清晰的服务分层和依赖管理。

## 架构设计原则

### 1. 单一职责原则 (SRP)
每个服务只负责一个特定的业务领域，避免功能重叠和职责混乱。

### 2. 依赖注入 (DI)
通过ServiceLocator管理服务实例和依赖关系，便于测试和维护。

### 3. 分层架构
- **UI层**: 页面和组件
- **服务层**: 业务逻辑服务
- **数据层**: PocketBase和本地存储

## 服务架构图

```
UI层 (Pages & Widgets)
        ↓
ServiceLocator (服务定位器)
        ↓
┌─────────────────────────────────────┐
│            服务层 (Services)          │
├─────────────────────────────────────┤
│ AuthService        (认证服务)        │
│ UserService        (用户管理服务)     │
│ SocialService      (社交功能服务)     │
│ FishingSpotService (钓点管理服务)     │
│ LocationService    (位置服务)        │
│ TileCacheService   (缓存服务)        │
└─────────────────────────────────────┘
        ↓
┌─────────────────────────────────────┐
│            数据层 (Data)             │
├─────────────────────────────────────┤
│ PocketBase        (后端数据库)       │
│ SharedPreferences (本地存储)         │
└─────────────────────────────────────┘
```

## 服务详细说明

### 1. AuthService (认证服务)

**文件**: `lib/services/auth_service_new.dart`

**职责**:
- 用户认证（登录、注册、登出）
- 会话管理和状态监听
- 自动登录功能
- 认证状态通知
- 权限检查

**主要方法**:
```dart
// 初始化
Future<void> initialize()

// 登录
Future<User?> login({required String email, required String password})

// 手机号登录
Future<User?> phoneLogin({required String phoneNumber})

// 注册
Future<User?> register({required String email, required String password, ...})

// 登出
Future<void> logout()

// 状态检查
bool get isLoggedIn
User? get currentUser
ValueListenable<User?> get currentUserNotifier
```

### 2. UserService (用户管理服务)

**文件**: `lib/services/user_service_new.dart`

**职责**:
- 用户信息管理和CRUD操作
- 用户查询和搜索
- 用户数据缓存
- 用户统计信息

**主要方法**:
```dart
// 用户查询
Future<User?> getUserById(String id)
Future<User?> getUserByUsername(String username)
Future<List<User>> searchUsers(String query)
Future<List<User>> getAllUsers()

// 用户管理
Future<bool> updateUser(User user)
Future<bool> updateUserAvatar(String userId, String avatarPath)

// 统计信息
Future<Map<String, int>> getUserStats(String userId)
```

### 3. SocialService (社交功能服务)

**文件**: `lib/services/social_service.dart`

**职责**:
- 用户关注/取消关注
- 点赞/取消点赞
- 评论管理
- 私信功能
- 收藏管理

**主要方法**:
```dart
// 关注功能
Future<bool> followUser(String targetUserId)
Future<bool> unfollowUser(String targetUserId)
Future<bool> isFollowing(String targetUserId)
Future<List<User>> getFollowing(String userId)
Future<List<User>> getFollowers(String userId)

// 点赞功能
Future<bool> likeSpot(String spotId)
Future<bool> unlikeSpot(String spotId)
Future<bool> isSpotLiked(String spotId)
Future<int> getSpotLikesCount(String spotId)

// 评论功能
Future<bool> addComment(String spotId, String content)
Future<bool> deleteComment(String commentId)

// 收藏功能
Future<bool> favoriteSpot(String spotId)
Future<bool> unfavoriteSpot(String spotId)
Future<bool> isSpotFavorited(String spotId)

// 私信功能
Future<Message?> sendMessage(String receiverId, String content)
Future<List<Message>> getConversation(String otherUserId)
```

### 4. FishingSpotService (钓点管理服务)

**文件**: `lib/services/fishing_spot_service_new.dart`

**职责**:
- 钓点CRUD操作
- 钓点搜索和筛选
- 地理位置查询
- 照片上传管理

**主要方法**:
```dart
// 钓点CRUD
Future<List<FishingSpot>> getAllSpots()
Future<FishingSpot?> getSpotById(String id)
Future<FishingSpot?> addSpot(FishingSpot spot)
Future<bool> updateSpot(FishingSpot spot)
Future<bool> deleteSpot(String spotId)

// 搜索和筛选
Future<List<FishingSpot>> searchSpots(String query)
Future<List<FishingSpot>> getSpotsInBounds({...})
Future<List<FishingSpot>> getUserSpots(String userId)

// 照片管理
Future<String?> addPhotoToSpot(String spotId, String photoPath, bool isPanorama)
Future<bool> deleteSpotPhoto(String photoId)
```

### 5. ServiceLocator (服务定位器)

**文件**: `lib/services/service_locator.dart`

**职责**:
- 服务实例管理和注册
- 依赖注入
- 服务生命周期管理
- 提供统一的服务访问接口

**使用方法**:
```dart
// 全局访问
final serviceLocator = ServiceLocator.instance;

// 便捷访问器
Services.auth.login(...);
Services.user.getUserById(...);
Services.social.followUser(...);
Services.fishingSpot.getAllSpots();
```

## 使用指南

### 1. 在应用启动时初始化服务

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化服务架构
  await serviceLocator.registerServices();
  await serviceLocator.initializeServices();
  
  runApp(MyApp());
}
```

### 2. 在页面中使用服务

```dart
class MyPage extends StatefulWidget {
  @override
  State<MyPage> createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> {
  @override
  void initState() {
    super.initState();
    
    // 监听认证状态变化
    Services.auth.currentUserNotifier.addListener(_onUserChanged);
  }
  
  void _onUserChanged() {
    setState(() {
      // 更新UI
    });
  }
  
  Future<void> _loadData() async {
    // 获取用户数据
    final users = await Services.user.getAllUsers();
    
    // 获取钓点数据
    final spots = await Services.fishingSpot.getAllSpots();
    
    setState(() {
      // 更新UI
    });
  }
  
  @override
  void dispose() {
    Services.auth.currentUserNotifier.removeListener(_onUserChanged);
    super.dispose();
  }
}
```

### 3. 错误处理

```dart
try {
  final user = await Services.auth.login(
    email: email,
    password: password,
  );
  // 处理成功情况
} catch (e) {
  // 处理错误
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('登录失败: $e')),
  );
}
```

## 迁移指南

### 从旧服务迁移到新服务

1. **认证相关代码**:
   ```dart
   // 旧代码
   final authService = PocketBaseAuthService();
   await authService.login(...);
   
   // 新代码
   await Services.auth.login(...);
   ```

2. **用户管理代码**:
   ```dart
   // 旧代码
   final userService = UserService();
   final users = await userService.getAllUsers();
   
   // 新代码
   final users = await Services.user.getAllUsers();
   ```

3. **社交功能代码**:
   ```dart
   // 旧代码 (分散在各个服务中)
   await userService.followUser(...);
   await fishingSpotService.addLike(...);
   
   // 新代码 (统一在社交服务中)
   await Services.social.followUser(...);
   await Services.social.likeSpot(...);
   ```

## 测试

### 服务测试页面

访问 `/dev/service-test` 路由可以测试新的服务架构功能（仅在开发模式下可用）。

### 单元测试

```dart
void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    
    setUp(() {
      authService = AuthService();
    });
    
    test('should login successfully', () async {
      // 测试登录功能
    });
  });
}
```

## 性能优化

### 1. 缓存策略
- 用户数据缓存5分钟
- 钓点数据缓存5分钟
- 区域钓点数据独立缓存

### 2. 懒加载
- 服务按需初始化
- 数据按需加载

### 3. 内存管理
- 及时清理监听器
- 合理使用dispose方法

## 故障排除

### 常见问题

1. **服务未初始化**
   - 确保在main.dart中正确初始化服务
   - 检查服务注册顺序

2. **依赖循环**
   - 检查服务间的依赖关系
   - 避免相互依赖

3. **内存泄漏**
   - 及时移除监听器
   - 正确使用dispose方法

### 调试工具

```dart
// 打印服务状态
serviceLocator.printServiceStatus();

// 获取服务健康状态
final health = serviceLocator.getServiceHealth();
```

## 未来扩展

### 计划中的功能

1. **缓存服务增强**
   - 更智能的缓存策略
   - 离线数据同步

2. **推送通知服务**
   - 消息推送
   - 系统通知

3. **分析服务**
   - 用户行为分析
   - 性能监控

### 扩展指南

1. 创建新服务类
2. 在ServiceLocator中注册
3. 定义服务接口
4. 实现业务逻辑
5. 添加测试用例

---

**注意**: 这是重构后的新架构，旧的服务文件（如`auth_service.dart`、`pocketbase_auth_service.dart`等）将逐步被替换。
