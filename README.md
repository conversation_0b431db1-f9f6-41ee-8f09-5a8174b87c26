# 鱼窝子 (YuYiWo)

一个基于 Flutter 和 PocketBase 的钓鱼社交应用。

## 项目简介

鱼窝子是一个专为钓鱼爱好者设计的社交平台，用户可以：
- 分享钓点位置和信息
- 上传钓鱼照片和全景图
- 与其他钓友交流互动
- 收藏和评价钓点

## 技术栈

- **前端**: Flutter (Dart)
- **后端**: PocketBase
- **地图**: Flutter Map
- **状态管理**: StatefulWidget
- **本地存储**: SharedPreferences

## 开始使用

### 环境要求

- Flutter SDK >= 3.7.2
- Dart SDK >= 3.7.2
- PocketBase 服务器

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd yuyiwo_pocketbase
```

2. 安装依赖
```bash
flutter pub get
```

3. 配置 PocketBase 服务器
在 `lib/config/app_config.dart` 中修改服务器地址：
```dart
String get pocketBaseUrl {
  return 'http://your-pocketbase-server:8090';
}
```

4. 运行应用
```bash
flutter run
```

## 主要功能

### 🗺️ 地图浏览
- 交互式地图界面
- 钓点标记和信息显示
- 位置搜索和导航

### 📍 钓点管理
- 添加新钓点
- 上传照片和全景图
- 钓点评价和收藏

### 👥 社交功能
- 用户注册和登录
- 关注其他钓友
- 评论和点赞

### 🔧 开发工具
- 内置测试页面（仅开发模式）
- PocketBase 连接测试
- 用户和数据管理

## 项目结构

```
lib/
├── config/          # 配置文件
├── models/          # 数据模型
├── pages/           # 页面组件
├── services/        # 业务逻辑服务
├── widgets/         # 可复用组件
└── main.dart        # 应用入口
```

## 开发指南

### 开发模式功能

在开发模式下，应用提供额外的测试和调试功能：
- PocketBase 测试页面
- 开发者工具菜单
- 详细的日志输出

### 构建发布版本

```bash
flutter build apk --release
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。