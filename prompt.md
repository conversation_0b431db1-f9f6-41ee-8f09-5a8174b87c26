请帮我写一份关于 PocketBase 使用的详细教程，具体背景如下：

我已经在服务器 `117.72.60.131` 上运行了 PocketBase 后端，客户端是使用 Flutter 开发的，前端通过 `pocketbase.dart` 库连接后端。

请逐项说明以下内容，适合初学者阅读：


2. **如何注册新用户**，包括通过 API 调用注册流程（使用 `pocketbase.dart`）。
3. **如何使用用户名和密码登录**，并保持会话状态（session），包括登录代码示例。
4. **如何根据 Flutter 中的模型类，在 PocketBase 中创建对应的数据集合（Collection）**，并说明字段如何匹配。
5. **如何在 Flutter 中实现对该数据表的增、查操作**，包括写入一条数据、读取数据列表的代码示例。

请结合 Flutter 与 `pocketbase.dart`，用代码示例逐步解释上述问题。务必确保示例能直接运行，避免使用未说明的变量或方法。

