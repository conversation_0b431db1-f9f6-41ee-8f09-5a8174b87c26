{"mcpServers": {"github.com/upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/otherWS/yuwuzi/yuyiwo"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}}}