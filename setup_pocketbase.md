🚀 PocketBase 设置指南
1. 创建 users 集合
// 字段配置
{
  "username": "text", // 必填，唯一
  "email": "email",   // 必填，唯一，用于认证
  "nickname": "text", // 必填，显示名称
  "phone": "text",    // 可选
  "avatar": "file",   // 可选，头像文件
  "bio": "text",      // 可选，个人简介
  "points": "number", // 默认0，积分
  "lastLoginAt": "date" // 可选，最后登录时间
}
2. 创建 fishing_spots 集合
// 字段配置
{
  "name": "text",        // 必填，钓点名称
  "description": "text", // 可选，描述
  "latitude": "number",  // 必填，纬度
  "longitude": "number", // 必填，经度
  "user_id": "relation", // 必填，关联到 users
  "address": "text",     // 可选，地址
  "spot_type": "text",   // 可选，钓点类型
  "fish_types": "text",  // 可选，鱼类信息
  "is_public": "bool",   // 默认true，是否公开
  "status": "text"       // 默认"active"，状态
}
3. 创建 user_follows 集合
// 字段配置
{
  "follower_id": "relation",  // 必填，关联到 users
  "following_id": "relation"  // 必填，关联到 users
}
4. 创建 user_favorites 集合
// 字段配置
{
  "user_id": "relation", // 必填，关联到 users
  "spot_id": "relation"  // 必填，关联到 fishing_spots
}
5. 创建 spot_photos 集合
// 字段配置
{
  "spot_id": "relation",    // 必填，关联到 fishing_spots
  "user_id": "relation",    // 必填，关联到 users
  "filename": "text",       // 必填，文件名
  "url": "text",           // 必填，照片URL
  "type": "text",          // 默认"normal"，类型
  "description": "text",    // 可选，描述
  "sort_order": "number"   // 默认0，排序
}
6. 创建 spot_likes 集合
// 字段配置
{
  "user_id": "relation", // 必填，关联到 users
  "spot_id": "relation", // 必填，关联到 fishing_spots
  "type": "text"         // 必填，"like" 或 "dislike"
}
7. 创建 spot_comments 集合
// 字段配置
{
  "spot_id": "relation", // 必填，关联到 fishing_spots
  "user_id": "relation", // 必填，关联到 users
  "content": "text",     // 必填，评论内容
  "rating": "number"     // 可选，评分 1-5
}
8. 权限设置建议
// users 集合
{
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\" || id = @request.auth.id",
  "createRule": "",  // 允许任何人注册新用户
  "updateRule": "id = @request.auth.id",
  "deleteRule": "id = @request.auth.id"
}

// fishing_spots 集合
{
  "listRule": "is_public = true || user_id = @request.auth.id",
  "viewRule": "is_public = true || user_id = @request.auth.id",
  "createRule": "@request.auth.id != """,
  "updateRule": "user_id = @request.auth.id",
  "deleteRule": "user_id = @request.auth.id"
}

// 其他关系集合
{
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\"",
  "createRule": "@request.auth.id != \"\"",
  "updateRule": "user_id = @request.auth.id",
  "deleteRule": "user_id = @request.auth.id"
}
🔄 下一步工作
1. 在 PocketBase Admin UI 中创建上述集合
2. 配置字段类型和约束
3. 设置权限规则
4. 测试用户注册功能
5. 逐步迁移现有服务类以使用新的模型结构

📱 Flutter/Dart 集成
1. 添加依赖到 pubspec.yaml:
```yaml
dependencies:
  pocketbase: ^0.8.0
```

2. 初始化 PocketBase 客户端:
```dart
final pb = PocketBase('http://127.0.0.1:8090');
```

3. 用户认证示例:
```dart
// 邮箱/密码登录
await pb.collection('users').authWithPassword(
  '<EMAIL>',
  '12345678',
);

// 获取当前用户
final authData = pb.authStore.model;
```

4. 实时订阅示例:
```dart
pb.collection('fishing_spots').subscribe('*', (e) {
  print('实时更新: ${e.record}');
});
```

🔒 API 认证配置
1. 在 PocketBase Admin UI 设置 -> API 中:
- 启用 JWT 认证
- 设置 Token 有效期 (建议 1-7 天)
- 配置 CORS 允许 Flutter 应用域名

现在您可以按照这个指南在 PocketBase 中设置数据库结构并集成到 Flutter 应用了！
